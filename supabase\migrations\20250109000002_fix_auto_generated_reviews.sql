/*
  # Fix Auto-Generated Reviews System
  
  This migration fixes issues with the auto-generated reviews system that were
  overridden by later migrations. It ensures:
  
  1. Proper columns exist (user_name, is_auto_generated)
  2. Correct constraints that allow multiple auto-generated reviews
  3. Proper RLS policies for both regular and auto-generated reviews
  4. Service role permissions for auto-generated review management
*/

-- Ensure the required columns exist
DO $$
BEGIN
  -- Add is_auto_generated column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'reviews' AND column_name = 'is_auto_generated'
  ) THEN
    ALTER TABLE reviews ADD COLUMN is_auto_generated boolean DEFAULT false;
  END IF;

  -- Add user_name column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'reviews' AND column_name = 'user_name'
  ) THEN
    ALTER TABLE reviews ADD COLUMN user_name text;
  END IF;
END $$;

-- Fix the unique constraint issue
DO $$
BEGIN
  -- Drop the problematic unique constraint that prevents multiple auto-generated reviews
  IF EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'reviews_product_id_user_id_key' 
    AND table_name = 'reviews'
  ) THEN
    ALTER TABLE reviews DROP CONSTRAINT reviews_product_id_user_id_key;
  END IF;
  
  -- Create a partial unique index that only applies to non-auto-generated reviews
  -- This allows multiple auto-generated reviews per product but maintains uniqueness for real users
  DROP INDEX IF EXISTS reviews_product_user_unique;
  CREATE UNIQUE INDEX reviews_product_user_unique 
    ON reviews (product_id, user_id) 
    WHERE is_auto_generated = false AND user_id IS NOT NULL;
END $$;

-- Add check constraint to ensure data integrity
DO $$
BEGIN
  -- Drop existing constraint if it exists
  IF EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'reviews_auto_generated_check' 
    AND table_name = 'reviews'
  ) THEN
    ALTER TABLE reviews DROP CONSTRAINT reviews_auto_generated_check;
  END IF;
  
  -- Add the constraint
  ALTER TABLE reviews ADD CONSTRAINT reviews_auto_generated_check 
    CHECK (
      (is_auto_generated = false AND user_id IS NOT NULL) OR 
      (is_auto_generated = true AND user_name IS NOT NULL)
    );
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_reviews_auto_generated ON reviews(is_auto_generated);
CREATE INDEX IF NOT EXISTS idx_reviews_product_auto ON reviews(product_id, is_auto_generated);
CREATE INDEX IF NOT EXISTS idx_reviews_user_name ON reviews(user_name) WHERE is_auto_generated = true;

-- Update RLS policies to handle both regular and auto-generated reviews properly
-- Drop existing policies first
DROP POLICY IF EXISTS "Reviews are viewable by everyone" ON reviews;
DROP POLICY IF EXISTS "Users can create own reviews" ON reviews;
DROP POLICY IF EXISTS "Users can update own reviews" ON reviews;
DROP POLICY IF EXISTS "Users can delete own reviews" ON reviews;
DROP POLICY IF EXISTS "Admins can manage all reviews" ON reviews;
DROP POLICY IF EXISTS "Service role can create auto-generated reviews" ON reviews;
DROP POLICY IF EXISTS "Service role can view all reviews" ON reviews;
DROP POLICY IF EXISTS "Service role can update auto-generated reviews" ON reviews;
DROP POLICY IF EXISTS "Service role can delete auto-generated reviews" ON reviews;

-- Allow everyone to view all reviews (both regular and auto-generated)
CREATE POLICY "Reviews are viewable by everyone"
  ON reviews
  FOR SELECT
  TO public
  USING (true);

-- Allow authenticated users to create their own reviews (non-auto-generated only)
CREATE POLICY "Users can create own reviews"
  ON reviews
  FOR INSERT
  TO public
  WITH CHECK (auth.uid() = user_id AND is_auto_generated = false);

-- Allow users to update their own reviews (non-auto-generated only)
CREATE POLICY "Users can update own reviews"
  ON reviews
  FOR UPDATE
  TO public
  USING (auth.uid() = user_id AND is_auto_generated = false)
  WITH CHECK (auth.uid() = user_id AND is_auto_generated = false);

-- Allow users to delete their own reviews (non-auto-generated only)
CREATE POLICY "Users can delete own reviews"
  ON reviews
  FOR DELETE
  TO public
  USING (auth.uid() = user_id AND is_auto_generated = false);

-- Allow service role to manage auto-generated reviews
CREATE POLICY "Service role can create auto-generated reviews"
  ON reviews
  FOR INSERT
  TO service_role
  WITH CHECK (is_auto_generated = true);

CREATE POLICY "Service role can view all reviews"
  ON reviews
  FOR SELECT
  TO service_role
  USING (true);

CREATE POLICY "Service role can update auto-generated reviews"
  ON reviews
  FOR UPDATE
  TO service_role
  USING (is_auto_generated = true)
  WITH CHECK (is_auto_generated = true);

CREATE POLICY "Service role can delete auto-generated reviews"
  ON reviews
  FOR DELETE
  TO service_role
  USING (is_auto_generated = true);

-- Allow admins to manage all reviews
CREATE POLICY "Admins can manage all reviews"
  ON reviews
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Recreate the cleanup function for auto-generated reviews
CREATE OR REPLACE FUNCTION cleanup_auto_generated_reviews(product_uuid UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Remove auto-generated reviews for a specific product
  DELETE FROM reviews 
  WHERE product_id = product_uuid AND is_auto_generated = true;
  
  -- The trigger will automatically recalculate the product rating
END;
$$;

-- Grant execute permission to service role
GRANT EXECUTE ON FUNCTION cleanup_auto_generated_reviews(UUID) TO service_role;

-- Update existing auto-generated reviews to have proper flags (if any exist)
-- This is a safety measure in case there are existing reviews that should be marked as auto-generated
UPDATE reviews 
SET is_auto_generated = true 
WHERE user_name IS NOT NULL 
  AND user_id IS NULL 
  AND is_auto_generated = false;
