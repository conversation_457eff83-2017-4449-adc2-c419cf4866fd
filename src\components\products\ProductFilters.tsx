import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

interface ProductFiltersProps {
  categories: any[];
  filters: {
    category: string;
    minPrice: string;
    maxPrice: string;
    sortBy: string;
  };
  onFiltersChange: (filters: any) => void;
}

const ProductFilters = ({ categories, filters, onFiltersChange }: ProductFiltersProps) => {
  const updateFilter = (key: string, value: string) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const handleCategoryChange = (value: string) => {
    // Map the special "all-categories" value back to empty string
    const categoryValue = value === 'all-categories' ? '' : value;
    updateFilter('category', categoryValue);
  };

  const clearFilters = () => {
    onFiltersChange({
      category: '',
      minPrice: '',
      maxPrice: '',
      sortBy: 'name'
    });
  };

  return (
    <Card className="lg:sticky lg:top-4">
      <CardHeader className="pb-2 sm:pb-6">
        <CardTitle className="text-sm sm:text-lg">Filters</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3 sm:space-y-4">
        <div>
          <label className="text-xs sm:text-sm font-medium mb-1 sm:mb-2 block">Category</label>
          <Select value={filters.category || 'all-categories'} onValueChange={handleCategoryChange}>
            <SelectTrigger className="h-8 sm:h-10 text-xs sm:text-sm">
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-categories">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.slug}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-xs sm:text-sm font-medium mb-1 sm:mb-2 block">Price Range</label>
          <div className="grid grid-cols-2 gap-1 sm:gap-2">
            <Input
              type="number"
              placeholder="Min"
              value={filters.minPrice}
              onChange={(e) => updateFilter('minPrice', e.target.value)}
              className="h-8 sm:h-10 text-xs sm:text-sm"
            />
            <Input
              type="number"
              placeholder="Max"
              value={filters.maxPrice}
              onChange={(e) => updateFilter('maxPrice', e.target.value)}
              className="h-8 sm:h-10 text-xs sm:text-sm"
            />
          </div>
        </div>

        <div>
          <label className="text-xs sm:text-sm font-medium mb-1 sm:mb-2 block">Sort By</label>
          <Select value={filters.sortBy} onValueChange={(value) => updateFilter('sortBy', value)}>
            <SelectTrigger className="h-8 sm:h-10 text-xs sm:text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="price">Price: Low to High</SelectItem>
              <SelectItem value="price desc">Price: High to Low</SelectItem>
              <SelectItem value="rating desc">Rating</SelectItem>
              <SelectItem value="created_at desc">Newest</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button
          variant="outline"
          onClick={clearFilters}
          className="w-full h-8 sm:h-10 text-xs sm:text-sm"
        >
          Clear Filters
        </Button>
      </CardContent>
    </Card>
  );
};

export default ProductFilters;