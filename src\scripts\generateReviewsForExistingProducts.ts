/**
 * <PERSON><PERSON>t to generate auto reviews for existing products that don't have reviews
 * This can be run manually or as a one-time migration
 */

import { generateReviewsForProductsWithoutReviews } from '@/services/autoRatingService';

export async function runReviewGeneration() {
  console.log('🚀 Starting auto review generation for existing products...');
  console.log('This will add 5-10 random Indian reviews (4-5 stars) to products with no or few reviews.');
  
  try {
    const result = await generateReviewsForProductsWithoutReviews();
    
    console.log('\n📊 Results:');
    console.log(`✅ Successfully processed: ${result.processedProducts} products`);
    
    if (result.errors.length > 0) {
      console.log(`❌ Errors encountered: ${result.errors.length}`);
      result.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
    
    if (result.success) {
      console.log('\n🎉 Review generation completed successfully!');
      console.log('All eligible products now have auto-generated reviews with Indian names.');
    } else {
      console.log('\n⚠️  Review generation completed with some errors.');
      console.log('Check the error messages above for details.');
    }
    
    return result;
    
  } catch (error) {
    console.error('💥 Fatal error during review generation:', error);
    throw error;
  }
}

// Export for use in admin panel or other components
export { generateReviewsForProductsWithoutReviews };

// If running directly (not imported)
if (typeof window !== 'undefined' && (window as any).runReviewGeneration) {
  (window as any).runReviewGeneration = runReviewGeneration;
}
