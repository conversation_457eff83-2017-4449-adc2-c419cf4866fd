import { serve } from "https://deno.land/std@0.201.0/http/server.ts";
import {
  createClient,
  SupabaseClient,
} from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": Deno.env.get("ALLOWED_ORIGIN") ?? "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
};

// Validate required ENVs at startup
const SUPABASE_URL = Deno.env.get("SUPABASE_URL");
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const SUPABASE_ANON_KEY = Deno.env.get("SUPABASE_ANON_KEY");

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY || !SUPABASE_ANON_KEY) {
  console.error("❌ Missing Supabase ENV (URL, SERVICE_ROLE_KEY, or ANON_KEY)");
  Deno.exit(1);
}

// Initialize Supabase clients
const supabaseServiceRole: SupabaseClient = createClient(
  SUPABASE_URL,
  SUPABASE_SERVICE_ROLE_KEY,
  { auth: { persistSession: false } }
);

const supabase: SupabaseClient = createClient(
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  { auth: { persistSession: false } }
);

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const {
      user_id,
      total_amount,
      shipping_address,
      order_items,
      customer_details,
      user_authenticated
    } = await req.json();

    console.log("📝 Creating order for user:", user_id || "guest");
    console.log("🔐 User authenticated:", user_authenticated);
    console.log("📧 Customer email:", customer_details?.email);

    // **SMART USER LINKING: Check if email exists in profiles and link automatically**
    let finalUserId = user_id;

    if (!user_id && customer_details?.email) {
      console.log("🔍 No user_id provided, checking if email exists in profiles:", customer_details.email);

      const { data: existingProfile, error: profileError } = await supabaseServiceRole
        .from("profiles")
        .select("id, email, first_name, last_name")
        .ilike("email", customer_details.email.toLowerCase().trim())
        .single();

      if (existingProfile && !profileError) {
        console.log("✅ Found existing profile for email, linking order:", {
          id: existingProfile.id,
          email: existingProfile.email,
          name: `${existingProfile.first_name} ${existingProfile.last_name}`
        });
        finalUserId = existingProfile.id;
      } else {
        console.log("ℹ️ No existing profile found for email, will create guest order");
      }
    } else if (user_id) {
      console.log("✅ User ID provided, using it directly:", user_id);
    }

    // **CRITICAL: Validate stock availability before creating order**
    const stockValidation = await validateStockAvailability(order_items);
    if (!stockValidation.valid) {
      console.error("❌ Stock validation failed:", stockValidation.errors);
      return new Response(JSON.stringify({ 
        error: "Insufficient stock",
        details: stockValidation.errors
      }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Create order in database
    // Use service role client for all orders to bypass RLS issues
    // This is safe because we validate the user_id and order data before creation
    const supabaseClient = supabaseServiceRole;

    console.log("🔐 Using service role client for order creation");
    console.log("👤 Final user ID for order:", finalUserId || "guest");

    const { data: orderData, error: orderError } = await supabaseClient
      .from("orders")
      .insert({
        user_id: finalUserId,
        total_amount: total_amount,
        status: "pending",
        payment_status: "pending",
        shipping_address: shipping_address,
      })
      .select()
      .single();

    if (orderError || !orderData) {
      console.error("❌ Order creation failed:", orderError);

      // Provide more specific error messages
      if (orderError?.code === '23503') {
        throw new Error('Invalid user reference - user profile not found');
      } else if (orderError?.code === '42501') {
        throw new Error('Permission denied - authentication required');
      } else {
        throw new Error(`Order creation failed: ${orderError?.message || 'Unknown error'}`);
      }
    }

    console.log("✅ Order created:", orderData.order_number);

    // Create order items
    const orderItemsWithOrderId = order_items.map((item: any) => ({
      ...item,
      order_id: orderData.id,
    }));

    const { error: itemsError } = await supabaseClient
      .from("order_items")
      .insert(orderItemsWithOrderId);

    if (itemsError) {
      console.error("❌ Order items creation failed:", itemsError);

      // Cleanup: delete the order if items creation failed
      try {
        await supabaseClient.from("orders").delete().eq("id", orderData.id);
        console.log("🧹 Cleaned up failed order:", orderData.order_number);
      } catch (cleanupError) {
        console.error("❌ Failed to cleanup order:", cleanupError);
      }

      throw new Error(`Order items creation failed: ${itemsError.message}`);
    }

    console.log("✅ Order items created");

    // **CRITICAL: Reserve stock quantities (temporary hold)**
    try {
      await reserveStockQuantities(order_items);
      console.log("✅ Stock quantities reserved for order:", orderData.order_number);
    } catch (stockError) {
      console.error("❌ Failed to reserve stock quantities:", stockError);
      // Cleanup: delete the order and items if stock reservation failed
      await supabase.from("orders").delete().eq("id", orderData.id);
      throw new Error("Stock reservation failed - order cancelled");
    }

    // Handle user profile and address saving
    if (user_id) {
      try {
        // Update/create profile
        if (customer_details) {
          const { error: profileError } = await supabase
            .from("profiles")
            .upsert({
              id: user_id,
              first_name: customer_details.first_name,
              last_name: customer_details.last_name,
              email: customer_details.email,
              phone: customer_details.phone,
              updated_at: new Date().toISOString(),
            });

          if (profileError) {
            console.error("⚠️ Profile update failed:", profileError);
          } else {
            console.log("✅ Profile updated/created");
          }
        }

        // Save shipping address
        await saveShippingAddress(user_id, shipping_address);
      } catch (saveError) {
        console.error("⚠️ Error saving address or updating profile:", saveError);
        // Don't throw, order creation is more important
      }
    }

    return new Response(JSON.stringify(orderData), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("❌ Error in create-order function:", error);
    return new Response(
      JSON.stringify({
        error: error.message,
        details: "Check server logs for more information",
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});

// **CRITICAL FUNCTION: Validate stock availability**
async function validateStockAvailability(orderItems: any[]): Promise<{valid: boolean, errors: string[]}> {
  const errors: string[] = [];
  
  for (const item of orderItems) {
    const { data: product, error } = await supabase
      .from("products")
      .select("id, name, stock_quantity, is_active")
      .eq("id", item.product_id)
      .single();

    if (error || !product) {
      errors.push(`Product ${item.product_id} not found`);
      continue;
    }

    if (!product.is_active) {
      errors.push(`Product "${product.name}" is no longer available`);
      continue;
    }

    if (product.stock_quantity < item.quantity) {
      errors.push(`Insufficient stock for "${product.name}". Available: ${product.stock_quantity}, Requested: ${item.quantity}`);
      continue;
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

// **CRITICAL FUNCTION: Reserve stock quantities (temporary hold)**
async function reserveStockQuantities(orderItems: any[]) {
  for (const item of orderItems) {
    const { product_id, quantity } = item;
    
    // Get current stock with row-level locking to prevent race conditions
    const { data: product, error: fetchError } = await supabase
      .from('products')
      .select('stock_quantity')
      .eq('id', product_id)
      .single();

    if (fetchError || !product) {
      throw new Error(`Failed to fetch product ${product_id} for stock reservation`);
    }

    const newStock = Math.max(0, product.stock_quantity - quantity);
    
    if (product.stock_quantity < quantity) {
      throw new Error(`Insufficient stock for product ${product_id}: ${product.stock_quantity} < ${quantity}`);
    }

    // Update stock quantity atomically
    const { error: updateError } = await supabase
      .from('products')
      .update({ 
        stock_quantity: newStock,
        updated_at: new Date().toISOString()
      })
      .eq('id', product_id)
      .eq('stock_quantity', product.stock_quantity); // Optimistic locking

    if (updateError) {
      throw new Error(`Failed to reserve stock for product ${product_id}: ${updateError.message}`);
    }

    console.log(`✅ Stock reserved for product ${product_id}: ${product.stock_quantity} → ${newStock}`);
  }
}

// Helper function to save shipping address
async function saveShippingAddress(userId: string, shippingAddress: any) {
  const { name, phone, address_line_1, address_line_2, city, state, postal_code, country } = shippingAddress;
  
  // Check if an identical address already exists for the user
  const { data: existingAddresses, error: fetchError } = await supabase
    .from("addresses")
    .select("id")
    .eq("user_id", userId)
    .eq("address_line_1", address_line_1)
    .eq("postal_code", postal_code)
    .limit(1);

  if (fetchError) {
    console.error("Error checking existing address:", fetchError);
    return;
  }

  if (!existingAddresses || existingAddresses.length === 0) {
    // If no identical address exists, insert it
    const { error: insertError } = await supabase
      .from("addresses")
      .insert({
        user_id: userId,
        name,
        phone,
        address_line_1,
        address_line_2,
        city,
        state,
        postal_code,
        country,
        is_default: false, // New addresses are not default by default
      });

    if (insertError) {
      console.error("Error saving address:", insertError);
    } else {
      console.log("✅ Shipping address saved to addresses table");
    }
  } else {
    console.log("ℹ️ Identical shipping address already exists, not saving again");
  }
}
