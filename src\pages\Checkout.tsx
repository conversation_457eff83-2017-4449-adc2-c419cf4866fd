import React, { useEffect, useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { ShoppingCart, User, MapPin, CreditCard, Eye, EyeOff, Loader2 } from 'lucide-react';

// Declare Cashfree interface
declare global {
  interface Window {
    Cashfree: any;
  }
}

const Checkout = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [totalAmount, setTotalAmount] = useState(0);
  const [deliveryCharge, setDeliveryCharge] = useState(0);
  const [freeDeliveryThreshold, setFreeDeliveryThreshold] = useState(0);
  const [cashfreeLoaded, setCashfreeLoaded] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [savedAddresses, setSavedAddresses] = useState([]);
  const [selectedAddressId, setSelectedAddressId] = useState('new'); // 'new' or address.id
  const [existingUserDetected, setExistingUserDetected] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    // Customer Info
    email: user?.email || '',
    password: '',
    firstName: '',
    lastName: '',
    phone: '',
    
    // Shipping Address
    name: '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'India',
    
    // User type - Always create account for non-logged users
    isExistingUser: !!user,
    createAccount: !user // Always true for guest users
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    fetchCartItems();
    fetchDeliverySettings();
    loadCashfreeScript();
    
    // Pre-fill user data if logged in
    if (user) {
      fetchUserProfile();
      fetchSavedAddresses();
    }
  }, [user]);

  useEffect(() => {
    if (cartItems.length > 0) {
      const subtotal = cartItems.reduce((sum, item) => sum + (item.products.price * item.quantity), 0);
      const shipping = subtotal >= freeDeliveryThreshold ? 0 : deliveryCharge;
      setTotalAmount(subtotal + shipping);
    }
  }, [cartItems, deliveryCharge, freeDeliveryThreshold]);

  // Check if user exists when email changes
  useEffect(() => {
    if (!user && formData.email && formData.email.includes('@')) {
      checkExistingUser(formData.email);
    }
  }, [formData.email, user]);

  const checkExistingUser = async (email) => {
    try {
      // Normalize email to lowercase for consistent checking
      const normalizedEmail = email.toLowerCase().trim();

      const { data, error } = await supabase
        .from('profiles')
        .select('id, first_name, last_name, phone')
        .ilike('email', normalizedEmail)
        .single();

      if (data && !error) {
        setExistingUserDetected(true);
        // Pre-fill form with existing user data
        setFormData(prev => ({
          ...prev,
          firstName: data.first_name || '',
          lastName: data.last_name || '',
          phone: data.phone || '',
          isExistingUser: true,
          createAccount: false // Don't create account for existing user
        }));
        toast.info('We found an existing account with this email. Please sign in or continue as guest.');
      } else {
        setExistingUserDetected(false);
        setFormData(prev => ({
          ...prev,
          isExistingUser: false,
          createAccount: true
        }));
      }
    } catch (error) {
      console.error('Error checking existing user:', error);
      setExistingUserDetected(false);
    }
  };

  const fetchUserProfile = async () => {
    if (!user) return;
    
    try {
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profile) {
        setFormData(prev => ({
          ...prev,
          firstName: profile.first_name || '',
          lastName: profile.last_name || '',
          phone: profile.phone || '',
          email: profile.email || user.email || ''
        }));
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  const fetchSavedAddresses = async () => {
    if (!user) return;
    try {
      const { data, error } = await supabase
        .from('addresses')
        .select('*')
        .eq('user_id', user.id)
        .order('is_default', { ascending: false });

      if (error) throw error;
      setSavedAddresses(data || []);
      
      // If there's a default address, pre-select it
      const defaultAddress = data.find(addr => addr.is_default);
      if (defaultAddress) {
        setSelectedAddressId(defaultAddress.id);
        setFormData(prev => ({
          ...prev,
          name: defaultAddress.name,
          addressLine1: defaultAddress.address_line_1,
          addressLine2: defaultAddress.address_line_2,
          city: defaultAddress.city,
          state: defaultAddress.state,
          postalCode: defaultAddress.postal_code,
          country: defaultAddress.country,
        }));
      }
    } catch (error) {
      console.error('Error fetching saved addresses:', error);
      toast.error('Failed to load saved addresses.');
    }
  };

  const fetchDeliverySettings = async () => {
    try {
      const { data, error } = await supabase
        .from('app_settings')
        .select('value')
        .eq('key', 'delivery_charge_settings')
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching delivery settings:', error);
      } else if (data) {
        setDeliveryCharge(data.value.flatDeliveryCharge || 0);
        setFreeDeliveryThreshold(data.value.freeDeliveryThreshold || 0);
      }
    } catch (error) {
      console.error('Error fetching delivery settings:', error);
    }
  };

  const fetchCartItems = async () => {
    // If user is logged in, fetch from database
    if (user) {
      const { data, error } = await supabase
        .from('cart_items')
        .select(`
          *,
          products (
            id,
            name,
            price,
            image_url,
            slug,
            stock_quantity,
            is_active
          )
        `)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error fetching cart items:', error);
        toast.error('Failed to load cart items');
        setCartItems([]);
      } else {
        // Filter out inactive products and validate stock
        const validItems = (data || []).filter(item => {
          if (!item.products?.is_active) {
            toast.error(`${item.products?.name} is no longer available and has been removed from your cart`);
            // Remove inactive product from cart
            supabase.from('cart_items').delete().eq('id', item.id);
            return false;
          }
          if (item.products.stock_quantity < item.quantity) {
            toast.warning(`${item.products.name} quantity reduced to available stock (${item.products.stock_quantity})`);
            // Update quantity to available stock
            supabase.from('cart_items').update({ quantity: item.products.stock_quantity }).eq('id', item.id);
            item.quantity = item.products.stock_quantity;
          }
          return item.products.stock_quantity > 0;
        });
        setCartItems(validItems);
      }
    } else {
      // For guest users, get from localStorage
      const guestCart = JSON.parse(localStorage.getItem('guestCart') || '[]');
      if (guestCart.length > 0) {
        // Fetch product details for guest cart items
        const productIds = guestCart.map(item => item.productId);
        const { data: products, error } = await supabase
          .from('products')
          .select('*')
          .in('id', productIds);

        if (error) {
          console.error('Error fetching products for guest cart:', error);
          toast.error('Failed to load cart items');
          setCartItems([]);
        } else {
          const cartWithProducts = guestCart.map(item => {
            const product = products?.find(p => p.id === item.productId);
            if (!product?.is_active) {
              toast.error(`${product?.name || 'A product'} is no longer available and has been removed from your cart`);
              return null;
            }
            if (product.stock_quantity < item.quantity) {
              toast.warning(`${product.name} quantity reduced to available stock (${product.stock_quantity})`);
              item.quantity = product.stock_quantity;
            }
            return {
              id: `guest-${item.productId}`,
              quantity: item.quantity,
              products: product
            };
          }).filter(item => item && item.products && item.products.stock_quantity > 0);

          // Update localStorage with corrected quantities
          const updatedGuestCart = cartWithProducts.map(item => ({
            productId: item.products.id,
            quantity: item.quantity,
            addedAt: new Date().toISOString()
          }));
          localStorage.setItem('guestCart', JSON.stringify(updatedGuestCart));

          setCartItems(cartWithProducts);
        }
      }
    }
    setLoading(false);
  };

  const loadCashfreeScript = () => {
    return new Promise((resolve) => {
      if (window.Cashfree) {
        setCashfreeLoaded(true);
        resolve(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://sdk.cashfree.com/js/v3/cashfree.js';
      script.onload = () => {
        console.log('✅ Cashfree script loaded successfully');
        setCashfreeLoaded(true);
        resolve(true);
      };
      script.onerror = (error) => {
        console.error('❌ Failed to load Cashfree script:', error);
        setCashfreeLoaded(false);
        resolve(false);
      };
      document.body.appendChild(script);
    });
  };

  const validateForm = () => {
    const newErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }

    // Password validation for new users only (not for existing users)
    if (!formData.isExistingUser && !user && !formData.password) {
      newErrors.password = 'Password is required';
    } else if (!formData.isExistingUser && !user && formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    // Name validation
    if (!formData.firstName) newErrors.firstName = 'First name is required';
    if (!formData.lastName) newErrors.lastName = 'Last name is required';
    if (!formData.phone) newErrors.phone = 'Phone number is required';

    // Address validation
    if (!formData.name) newErrors.name = 'Recipient name is required';
    if (!formData.addressLine1) newErrors.addressLine1 = 'Address is required';
    if (!formData.city) newErrors.city = 'City is required';
    if (!formData.state) newErrors.state = 'State is required';
    if (!formData.postalCode) newErrors.postalCode = 'Postal code is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleExistingUserOrder = async () => {
    try {
      console.log('🔍 Handling order for existing user with email:', formData.email);

      // Normalize email to lowercase for consistent checking
      const normalizedEmail = formData.email.toLowerCase().trim();

      // Get the existing user's profile with more details for validation
      const { data: existingProfile, error: profileError } = await supabase
        .from('profiles')
        .select('id, email, first_name, last_name, phone')
        .ilike('email', normalizedEmail)
        .single();

      if (profileError || !existingProfile) {
        console.error('❌ Could not find existing user profile:', profileError);
        console.error('❌ Profile error details:', {
          code: profileError?.code,
          message: profileError?.message,
          details: profileError?.details
        });
        throw new Error('Could not find existing user account');
      }

      console.log('✅ Found existing user profile:', {
        id: existingProfile.id,
        email: existingProfile.email,
        name: `${existingProfile.first_name} ${existingProfile.last_name}`
      });

      // Important: For existing users ordering as guests, we'll create the order
      // via Edge Function which uses service role to bypass RLS policies
      return existingProfile.id;
    } catch (error) {
      console.error('❌ Error handling existing user order:', error);
      throw error;
    }
  };

  const createGuestUser = async () => {
    try {
      console.log('🔐 Creating guest user account...');
      
      // Create user account
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          emailRedirectTo: `${window.location.origin}/`,
          data: {
            first_name: formData.firstName,
            last_name: formData.lastName
          }
        }
      });

      if (authError) {
        console.error('❌ Auth signup error:', authError);
        throw authError;
      }

      console.log('✅ User account created:', authData.user?.id);

      // The profile should be created automatically by the trigger
      // But let's also try to create it manually as a fallback
      if (authData.user) {
        try {
          console.log('📝 Creating profile manually as fallback...');
          
          // Wait a bit for the trigger to potentially complete
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Try to create profile using the service role via edge function
          const profileResponse = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-profile`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
            },
            body: JSON.stringify({
              user_id: authData.user.id,
              email: formData.email,
              first_name: formData.firstName,
              last_name: formData.lastName,
              phone: formData.phone
            }),
          });

          if (profileResponse.ok) {
            console.log('✅ Profile created via edge function');
          } else {
            console.log('⚠️ Edge function profile creation failed, but continuing...');
          }
        } catch (profileError) {
          console.error('⚠️ Manual profile creation failed:', profileError);
          // Don't throw here - the trigger might have worked
        }

        return authData.user;
      }
    } catch (error) {
      console.error('❌ Error creating guest user:', error);
      throw error;
    }
  };

  const createOrder = async (userId) => {
    try {
      console.log('📝 Creating order via Edge Function...');
      console.log('👤 User ID for order:', userId);
      console.log('🔐 Is user authenticated:', !!user);
      console.log('🔍 Existing user detected:', existingUserDetected);

      const shippingAddress = {
        name: formData.name,
        phone: formData.phone,
        address_line_1: formData.addressLine1,
        address_line_2: formData.addressLine2,
        city: formData.city,
        state: formData.state,
        postal_code: formData.postalCode,
        country: formData.country
      };

      const orderItems = cartItems.map(item => ({
        product_id: item.products.id,
        quantity: item.quantity,
        price: item.products.price
      }));

      // Create order via Edge Function to handle both guest and authenticated users
      // The Edge Function uses service role to bypass RLS policies for existing user orders
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          user_id: userId,
          total_amount: totalAmount,
          shipping_address: shippingAddress,
          order_items: orderItems,
          customer_details: {
            first_name: formData.firstName,
            last_name: formData.lastName,
            email: formData.email,
            phone: formData.phone
          },
          // Additional context for debugging
          user_authenticated: !!user
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Order creation failed:', errorText);
        throw new Error('Failed to create order');
      }

      const orderData = await response.json();
      console.log('✅ Order created:', orderData.order_number);
      return orderData;
    } catch (error) {
      console.error('❌ Error creating order:', error);
      throw error;
    }
  };

  const createCashfreeOrder = async (order, customerDetails) => {
    try {
      console.log('💳 Creating Cashfree order...');
      console.log('💰 Customer details for Cashfree:', customerDetails);
      
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-cashfree-order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          order_id: order.order_number,
          order_amount: totalAmount,
          order_currency: 'INR',
          customer_details: customerDetails,
          order_meta: {
            return_url: `${window.location.origin}/orders`,
          }
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Cashfree order creation failed:', errorText);
        throw new Error('Failed to create Cashfree order');
      }

      const cashfreeOrder = await response.json();
      console.log('✅ Cashfree order created:', cashfreeOrder.cf_order_id);
      return cashfreeOrder;
    } catch (error) {
      console.error('❌ Error creating Cashfree order:', error);
      throw error;
    }
  };

  const handlePayment = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fill in all required fields correctly');
      return;
    }

    if (!cashfreeLoaded) {
      toast.error('Payment system is loading. Please try again in a moment.');
      await loadCashfreeScript();
      if (!cashfreeLoaded) {
        toast.error('Unable to load payment system. Please refresh the page.');
        return;
      }
    }

    const cashfreeAppId = import.meta.env.VITE_CASHFREE_APP_ID;
    if (!cashfreeAppId) {
      toast.error('Payment system not configured. Please contact support.');
      return;
    }

    setProcessing(true);

    try {
      console.log('🚀 Starting payment process...');
      
      let currentUserId = user?.id;
      
      // Handle user account creation/detection for guest users
      if (!user) {
        if (existingUserDetected) {
          // User exists but is not logged in - use existing user ID
          try {
            currentUserId = await handleExistingUserOrder();
            toast.success('Order will be added to your existing account!');
          } catch (error) {
            console.error('❌ Error linking to existing account:', error);
            toast.error('Could not link to existing account. Please sign in or try again.');
            setProcessing(false);
            return;
          }
        } else {
          // New user - create account
          try {
            const newUser = await createGuestUser();
            if (newUser) {
              currentUserId = newUser.id;
              toast.success('Account created! You can verify your email later to access your orders.');
            }
          } catch (error) {
            console.error('❌ Error creating account:', error);
            // For new users, we can still continue with null user_id for true guest checkout
            toast.warning('Account creation failed, but you can still place the order as a guest');
            currentUserId = null;
          }
        }
      }

      // Create order in our database
      let order;
      try {
        order = await createOrder(currentUserId || null);
        if (!order) {
          throw new Error('Order creation returned null');
        }
      } catch (orderError) {
        console.error('❌ Order creation failed:', orderError);

        toast.error('Failed to create order. Please try again.');

        setProcessing(false);
        return;
      }

      // Prepare customer details for Cashfree - use form data directly
      const customerDetails = {
        customer_id: currentUserId || `guest-${Date.now()}`,
        customer_name: `${formData.firstName} ${formData.lastName}`,
        customer_email: formData.email,
        customer_phone: formData.phone,
      };

      console.log('💰 Customer details for Cashfree:', customerDetails);

      // Create Cashfree order
      let cashfreeOrder;
      try {
        cashfreeOrder = await createCashfreeOrder(order, customerDetails);
      } catch (cashfreeError) {
        console.error('❌ Cashfree order creation failed:', cashfreeError);
        toast.error('Payment gateway error. Please try again.');
        setProcessing(false);
        return;
      }

      // Initialize Cashfree
      const cashfree = window.Cashfree({
        mode: import.meta.env.VITE_CASHFREE_MODE || 'production',
      });

      const checkoutOptions = {
        paymentSessionId: cashfreeOrder.payment_session_id,
        redirectTarget: '_self',
      };

      console.log('💰 Initiating Cashfree payment...');

      // Handle payment
      cashfree.checkout(checkoutOptions).then(async (result) => {
        console.log('📋 Payment result:', result);
        
        if (result.error) {
          console.error('❌ Payment failed:', result.error);
          toast.error(`Payment failed: ${result.error.message}`);
          setProcessing(false);
        } else if (result.redirect) {
          console.log('🔄 Payment redirect:', result.redirect);
          toast.success('Payment initiated successfully! Redirecting...');
        } else {
          console.log('✅ Payment completed:', result);
          toast.success('Payment successful! Order confirmed. Redirecting to orders...');
          
          // Clear cart after successful payment
          if (user) {
            await supabase
              .from('cart_items')
              .delete()
              .eq('user_id', user.id);
          } else {
            localStorage.removeItem('guestCart');
          }

          setTimeout(() => {
            navigate('/orders');
          }, 2000);
        }
      }).catch((error) => {
        console.error('❌ Cashfree checkout error:', error);
        toast.error('Payment failed. Please try again.');
        setProcessing(false);
      });

    } catch (error) {
      console.error('❌ Payment initiation error:', error);
      toast.error('Failed to initiate payment. Please try again.');
      setProcessing(false);
    }
  };

  const getSubtotal = () => {
    return cartItems.reduce((sum, item) => sum + (item.products.price * item.quantity), 0);
  };

  const getShippingCost = () => {
    const subtotal = getSubtotal();
    return subtotal >= freeDeliveryThreshold ? 0 : deliveryCharge;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="bg-gray-200 h-32 rounded-lg"></div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg mb-6">Your cart is empty. Nothing to checkout.</p>
            <Link to="/products">
              <Button className="bg-festive-red hover:bg-festive-red/90">
                Continue Shopping
              </Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-4 sm:py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-2xl sm:text-3xl font-playfair font-bold text-festive-red mb-6 sm:mb-8 text-center">
            Checkout
          </h1>

          <form onSubmit={handlePayment}>
            <div className="grid lg:grid-cols-3 gap-6 lg:gap-8">
              {/* Left Column - Customer Info & Shipping */}
              <div className="lg:col-span-2 space-y-4 sm:space-y-6">
                
                {/* Customer Information */}
                <Card>
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                      <User className="h-4 w-4 sm:h-5 sm:w-5" />
                      Customer Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 pt-0">
                    {!user && existingUserDetected && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <p className="text-blue-800 text-sm">
                          <strong>Existing Account Detected:</strong> We found an account with this email. Your order will be added to your existing account. 
                          <Link to="/auth" className="text-blue-600 underline ml-1">Sign in here</Link> if you want to access saved addresses.
                        </p>
                      </div>
                    )}

                    {!user && !existingUserDetected && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                        <p className="text-green-800 text-sm">
                          <strong>Account Creation:</strong> An account will be created for you to track your orders and future purchases.
                        </p>
                      </div>
                    )}

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName" className="text-sm font-medium">First Name *</Label>
                        <Input
                          id="firstName"
                          value={formData.firstName}
                          onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                          className={`mt-1 h-11 ${errors.firstName ? 'border-red-500' : ''}`}
                          placeholder="Enter first name"
                        />
                        {errors.firstName && <p className="text-red-500 text-xs mt-1">{errors.firstName}</p>}
                      </div>
                      <div>
                        <Label htmlFor="lastName" className="text-sm font-medium">Last Name *</Label>
                        <Input
                          id="lastName"
                          value={formData.lastName}
                          onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                          className={`mt-1 h-11 ${errors.lastName ? 'border-red-500' : ''}`}
                          placeholder="Enter last name"
                        />
                        {errors.lastName && <p className="text-red-500 text-xs mt-1">{errors.lastName}</p>}
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="email" className="text-sm font-medium">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                        className={`mt-1 h-11 ${errors.email ? 'border-red-500' : ''}`}
                        placeholder="Enter email address"
                        disabled={!!user}
                      />
                      {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                    </div>

                    {!user && !existingUserDetected && (
                      <div>
                        <Label htmlFor="password" className="text-sm font-medium">Password *</Label>
                        <div className="relative mt-1">
                          <Input
                            id="password"
                            type={showPassword ? 'text' : 'password'}
                            value={formData.password}
                            onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                            className={`h-11 pr-10 ${errors.password ? 'border-red-500' : ''}`}
                            placeholder="Minimum 6 characters"
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                          >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </button>
                        </div>
                        {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
                      </div>
                    )}

                    <div>
                      <Label htmlFor="phone" className="text-sm font-medium">Phone Number *</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                        className={`mt-1 h-11 ${errors.phone ? 'border-red-500' : ''}`}
                        placeholder="+91 9876543210"
                      />
                      {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
                    </div>
                  </CardContent>
                </Card>

                {/* Shipping Address */}
                <Card>
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                      <MapPin className="h-4 w-4 sm:h-5 sm:w-5" />
                      Shipping Address
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 pt-0">
                    {user && savedAddresses.length > 0 && (
                      <div className="mb-6">
                        <h3 className="font-semibold mb-2">Use a saved address:</h3>
                        <RadioGroup
                          value={selectedAddressId}
                          onValueChange={(value) => {
                            setSelectedAddressId(value);
                            if (value === 'new') {
                              setFormData(prev => ({
                                ...prev,
                                name: '',
                                addressLine1: '',
                                addressLine2: '',
                                city: '',
                                state: '',
                                postalCode: '',
                                country: 'India',
                              }));
                            } else {
                              const selected = savedAddresses.find(addr => addr.id === value);
                              if (selected) {
                                setFormData(prev => ({
                                  ...prev,
                                  name: selected.name,
                                  addressLine1: selected.address_line_1,
                                  addressLine2: selected.address_line_2,
                                  city: selected.city,
                                  state: selected.state,
                                  postalCode: selected.postal_code,
                                  country: selected.country,
                                }));
                              }
                            }
                          }}
                          className="space-y-2"
                        >
                          {savedAddresses.map((address) => (
                            <div key={address.id} className="flex items-center space-x-2 p-3 border rounded-md">
                              <RadioGroupItem value={address.id} id={`address-${address.id}`} />
                              <Label htmlFor={`address-${address.id}`} className="flex-1 cursor-pointer">
                                <p className="font-medium">{address.name}</p>
                                <p className="text-sm text-gray-600">{address.address_line_1}, {address.address_line_2 && `${address.address_line_2}, `}{address.city}, {address.state} - {address.postal_code}</p>
                              </Label>
                            </div>
                          ))}
                          <div className="flex items-center space-x-2 p-3 border rounded-md">
                            <RadioGroupItem value="new" id="address-new" />
                            <Label htmlFor="address-new" className="cursor-pointer">
                              Enter a new address
                            </Label>
                          </div>
                        </RadioGroup>
                        <Separator className="my-4" />
                      </div>
                    )}

                    <div>
                      <Label htmlFor="name" className="text-sm font-medium">Recipient Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        className={`mt-1 h-11 ${errors.name ? 'border-red-500' : ''}`}
                        placeholder="Full name of the person receiving the order"
                        disabled={selectedAddressId !== 'new'}
                      />
                      {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
                    </div>

                    <div>
                      <Label htmlFor="addressLine1" className="text-sm font-medium">Address Line 1 *</Label>
                      <Input
                        id="addressLine1"
                        value={formData.addressLine1}
                        onChange={(e) => setFormData(prev => ({ ...prev, addressLine1: e.target.value }))}
                        className={`mt-1 h-11 ${errors.addressLine1 ? 'border-red-500' : ''}`}
                        placeholder="House number, street name"
                        disabled={selectedAddressId !== 'new'}
                      />
                      {errors.addressLine1 && <p className="text-red-500 text-xs mt-1">{errors.addressLine1}</p>}
                    </div>

                    <div>
                      <Label htmlFor="addressLine2" className="text-sm font-medium">Address Line 2</Label>
                      <Input
                        id="addressLine2"
                        value={formData.addressLine2}
                        onChange={(e) => setFormData(prev => ({ ...prev, addressLine2: e.target.value }))}
                        className="mt-1 h-11"
                        placeholder="Apartment, suite, unit, building, floor, etc."
                        disabled={selectedAddressId !== 'new'}
                      />
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="city" className="text-sm font-medium">City *</Label>
                        <Input
                          id="city"
                          value={formData.city}
                          onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                          className={`mt-1 h-11 ${errors.city ? 'border-red-500' : ''}`}
                          placeholder="Enter city"
                          disabled={selectedAddressId !== 'new'}
                        />
                        {errors.city && <p className="text-red-500 text-xs mt-1">{errors.city}</p>}
                      </div>
                      <div>
                        <Label htmlFor="state" className="text-sm font-medium">State *</Label>
                        <Input
                          id="state"
                          value={formData.state}
                          onChange={(e) => setFormData(prev => ({ ...prev, state: e.target.value }))}
                          className={`mt-1 h-11 ${errors.state ? 'border-red-500' : ''}`}
                          placeholder="Enter state"
                          disabled={selectedAddressId !== 'new'}
                        />
                        {errors.state && <p className="text-red-500 text-xs mt-1">{errors.state}</p>}
                      </div>
                      <div className="sm:col-span-2 lg:col-span-1">
                        <Label htmlFor="postalCode" className="text-sm font-medium">Postal Code *</Label>
                        <Input
                          id="postalCode"
                          value={formData.postalCode}
                          onChange={(e) => setFormData(prev => ({ ...prev, postalCode: e.target.value }))}
                          className={`mt-1 h-11 ${errors.postalCode ? 'border-red-500' : ''}`}
                          placeholder="110001"
                          disabled={selectedAddressId !== 'new'}
                        />
                        {errors.postalCode && <p className="text-red-500 text-xs mt-1">{errors.postalCode}</p>}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Order Summary & Payment */}
              <div className="lg:col-span-1">
                <div className="lg:sticky lg:top-4 space-y-4 sm:space-y-6">
                  {/* Order Summary */}
                  <Card>
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                        <ShoppingCart className="h-4 w-4 sm:h-5 sm:w-5" />
                        Order Summary
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4 pt-0">
                      {cartItems.map((item) => (
                        <div key={item.id} className="flex items-start gap-3 py-3 border-b border-gray-100 last:border-b-0">
                          <img
                            src={item.products?.image_url || '/placeholder.svg'}
                            alt={item.products?.name}
                            className="w-14 h-14 sm:w-16 sm:h-16 object-cover rounded-lg flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-sm sm:text-base line-clamp-2 mb-2">{item.products?.name}</h4>
                            <div className="flex flex-col gap-1">
                              <div className="flex justify-between items-center">
                                <span className="text-xs sm:text-sm text-gray-600">Qty: {item.quantity}</span>
                                <span className="text-xs sm:text-sm text-gray-600">₹{item.products?.price} each</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-xs sm:text-sm font-medium">Total:</span>
                                <span className="font-semibold text-sm sm:text-base text-festive-red">
                                  ₹{(item.quantity * item.products?.price).toFixed(2)}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                      
                      <Separator />
                      
                      <div className="space-y-3 bg-gray-50 p-4 rounded-lg">
                        <div className="flex justify-between text-sm sm:text-base">
                          <span className="text-gray-600">Subtotal:</span>
                          <span className="font-medium">₹{getSubtotal().toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between text-sm sm:text-base">
                          <span className="text-gray-600">Shipping:</span>
                          <span className={`font-medium ${getShippingCost() === 0 ? "text-green-600" : ""}`}>
                            {getShippingCost() === 0 ? 'Free' : `₹${getShippingCost().toFixed(2)}`}
                          </span>
                        </div>
                        {freeDeliveryThreshold > 0 && getSubtotal() < freeDeliveryThreshold && (
                          <div className="text-xs sm:text-sm text-blue-600 bg-blue-50 p-2 rounded">
                            💡 Add ₹{(freeDeliveryThreshold - getSubtotal()).toFixed(2)} more for free shipping
                          </div>
                        )}
                        <Separator />
                        <div className="flex justify-between font-bold text-lg sm:text-xl">
                          <span>Total:</span>
                          <span className="text-festive-red">₹{totalAmount.toFixed(2)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Payment */}
                  <Card>
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                        <CreditCard className="h-4 w-4 sm:h-5 sm:w-5" />
                        Payment
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4 pt-0">
                      <div className="bg-blue-50 p-3 sm:p-4 rounded-lg">
                        <h4 className="font-medium mb-2 text-sm sm:text-base">Accepted Payment Methods</h4>
                        <div className="text-xs sm:text-sm text-gray-600 space-y-1">
                          <div>• UPI (Google Pay, PhonePe, Paytm)</div>
                          <div>• Credit/Debit Cards</div>
                          <div>• Net Banking</div>
                          <div>• Wallets</div>
                        </div>
                      </div>

                      <Button
                        type="submit"
                        className="w-full bg-festive-red hover:bg-festive-red/90 py-3 sm:py-4 text-base sm:text-lg font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                        disabled={processing || totalAmount === 0 || !cashfreeLoaded}
                      >
                        {processing ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing Payment...
                          </>
                        ) : (
                          <>
                            <CreditCard className="mr-2 h-4 w-4" />
                            Pay ₹{totalAmount.toFixed(2)}
                          </>
                        )}
                      </Button>

                      <p className="text-xs sm:text-sm text-gray-500 text-center">
                        🔒 Secure payment powered by Cashfree • UPI supported
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </form>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Checkout;