import React, { useState } from 'react';
import { useAdmin } from '@/hooks/useAdmin';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Star, TrendingUp, AlertCircle, CheckCircle, Play, RotateCcw } from 'lucide-react';
import { toast } from 'sonner';
import { generateReviewsForProductsWithoutReviews } from '@/services/autoRatingService';
import { supabase } from '@/integrations/supabase/client';

const ReviewManager = () => {
  const { isAdmin, loading: adminLoading } = useAdmin();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationResults, setGenerationResults] = useState<{
    processedProducts: number;
    errors: string[];
    success: boolean;
  } | null>(null);
  const [stats, setStats] = useState<{
    totalProducts: number;
    productsWithReviews: number;
    productsWithoutReviews: number;
    totalReviews: number;
    autoGeneratedReviews: number;
  } | null>(null);

  React.useEffect(() => {
    if (isAdmin) {
      loadStats();
    }
  }, [isAdmin]);

  const loadStats = async () => {
    try {
      // Get product stats
      const { data: products, error: productsError } = await supabase
        .from('products')
        .select('id, review_count')
        .eq('is_active', true);

      if (productsError) throw productsError;

      // Get review stats
      const { data: reviews, error: reviewsError } = await supabase
        .from('reviews')
        .select('id, is_auto_generated');

      if (reviewsError) throw reviewsError;

      const totalProducts = products?.length || 0;
      const productsWithReviews = products?.filter(p => (p.review_count || 0) > 0).length || 0;
      const productsWithoutReviews = totalProducts - productsWithReviews;
      const totalReviews = reviews?.length || 0;
      const autoGeneratedReviews = reviews?.filter(r => r.is_auto_generated).length || 0;

      setStats({
        totalProducts,
        productsWithReviews,
        productsWithoutReviews,
        totalReviews,
        autoGeneratedReviews
      });
    } catch (error) {
      console.error('Error loading stats:', error);
      toast.error('Failed to load statistics');
    }
  };

  const handleGenerateReviews = async () => {
    setIsGenerating(true);
    setGenerationResults(null);

    try {
      toast.info('Starting review generation... This may take a few moments.');
      
      const result = await generateReviewsForProductsWithoutReviews();
      
      setGenerationResults(result);
      
      if (result.success) {
        toast.success(`Successfully generated reviews for ${result.processedProducts} products!`);
      } else {
        toast.warning(`Completed with ${result.errors.length} errors. Check results below.`);
      }
      
      // Reload stats
      await loadStats();
      
    } catch (error) {
      console.error('Error generating reviews:', error);
      toast.error('Failed to generate reviews: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsGenerating(false);
    }
  };

  const handleResetStats = () => {
    setGenerationResults(null);
    loadStats();
  };

  if (adminLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="bg-gray-200 h-8 rounded w-1/3"></div>
            <div className="bg-gray-200 h-64 rounded"></div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8 max-w-6xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-festive-red font-playfair mb-2">
            Review Management System
          </h1>
          <p className="text-gray-600">
            Manage auto-generated reviews for products to improve social proof and customer confidence.
          </p>
        </div>

        {/* Statistics Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.totalProducts}</div>
                <div className="text-sm text-gray-600">Total Products</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{stats.productsWithReviews}</div>
                <div className="text-sm text-gray-600">With Reviews</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-orange-600">{stats.productsWithoutReviews}</div>
                <div className="text-sm text-gray-600">Without Reviews</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.totalReviews}</div>
                <div className="text-sm text-gray-600">Total Reviews</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-festive-red">{stats.autoGeneratedReviews}</div>
                <div className="text-sm text-gray-600">Auto Generated</div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Action Card */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5 text-festive-gold" />
              Auto Review Generation
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This system automatically generates 5-10 realistic reviews with Indian names for products
                that have no or very few reviews. Rating distribution: 60% five-star, 30% four-star, 10% three-star.
                Reviews are marked as auto-generated in the database.
              </AlertDescription>
            </Alert>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                onClick={handleGenerateReviews}
                disabled={isGenerating}
                className="bg-festive-red hover:bg-festive-red/90"
              >
                {isGenerating ? (
                  <>
                    <RotateCcw className="mr-2 h-4 w-4 animate-spin" />
                    Generating Reviews...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    Generate Reviews for Products
                  </>
                )}
              </Button>
              
              <Button 
                variant="outline" 
                onClick={handleResetStats}
                disabled={isGenerating}
              >
                <TrendingUp className="mr-2 h-4 w-4" />
                Refresh Statistics
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results Display */}
        {generationResults && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {generationResults.success ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-orange-600" />
                )}
                Generation Results
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <Badge variant={generationResults.success ? "default" : "secondary"}>
                  {generationResults.success ? "Success" : "Completed with Errors"}
                </Badge>
                <span className="text-sm text-gray-600">
                  Processed {generationResults.processedProducts} products
                </span>
              </div>

              {generationResults.errors.length > 0 && (
                <div>
                  <h4 className="font-medium text-red-600 mb-2">Errors ({generationResults.errors.length}):</h4>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3 max-h-40 overflow-y-auto">
                    {generationResults.errors.map((error, index) => (
                      <div key={index} className="text-sm text-red-700 mb-1">
                        {index + 1}. {error}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {generationResults.processedProducts > 0 && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    Successfully generated reviews for {generationResults.processedProducts} products.
                    Each product now has 5-10 reviews with realistic 3-5 star ratings from Indian customers.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        )}

        {/* Information Card */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>How It Works</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-festive-red text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
              <div>
                <h4 className="font-medium">Automatic Generation</h4>
                <p className="text-sm text-gray-600">New products automatically get 5-10 reviews when created</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-festive-red text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
              <div>
                <h4 className="font-medium">Indian Names & Reviews</h4>
                <p className="text-sm text-gray-600">Uses 200+ authentic Indian names with Rakhi-specific review content</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-festive-red text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
              <div>
                <h4 className="font-medium">Quality Ratings</h4>
                <p className="text-sm text-gray-600">60% get 5-star, 30% get 4-star, 10% get 3-star ratings for realistic distribution</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-festive-red text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
              <div>
                <h4 className="font-medium">Database Tracking</h4>
                <p className="text-sm text-gray-600">All auto-generated reviews are marked in the database for transparency</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
      <Footer />
    </div>
  );
};

export default ReviewManager;
