/*
  # Fix RLS policies for guest orders from existing users
  
  This migration fixes the issue where existing users ordering as guests
  cannot create orders due to RLS policy restrictions.
  
  The problem: 
  - Existing user orders as guest (not authenticated, so auth.uid() = NULL)
  - Order is created with user_id = existing_user_id  
  - RLS policy "Users can create own orders" requires auth.uid() = user_id
  - NULL ≠ existing_user_id, so policy fails
  
  The solution:
  - Add service role policy to allow Edge Functions to create orders for any user
  - Add service role policy for order_items as well
*/

-- Add service role policy for orders table
CREATE POLICY "Service role can create orders for any user"
  ON public.orders
  FOR INSERT
  TO service_role
  WITH CHECK (true);

-- Add service role policy for order_items table  
CREATE POLICY "Service role can create order items for any order"
  ON public.order_items
  FOR INSERT
  TO service_role
  WITH CHECK (true);

-- Also add service role policies for SELECT operations (needed for order creation process)
CREATE POLICY "Service role can view all orders"
  ON public.orders
  FOR SELECT
  TO service_role
  USING (true);

CREATE POLICY "Service role can view all order items"
  ON public.order_items
  FOR SELECT
  TO service_role
  USING (true);

-- Add service role policy for UPDATE operations (needed for order status updates)
CREATE POLICY "Service role can update all orders"
  ON public.orders
  FOR UPDATE
  TO service_role
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Service role can update all order items"
  ON public.order_items
  FOR UPDATE
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Add service role policy for DELETE operations (needed for cleanup)
CREATE POLICY "Service role can delete all orders"
  ON public.orders
  FOR DELETE
  TO service_role
  USING (true);

CREATE POLICY "Service role can delete all order items"
  ON public.order_items
  FOR DELETE
  TO service_role
  USING (true);
