/*
  # Add support for auto-generated reviews
  
  1. Changes
    - Add is_auto_generated column to reviews table
    - Add user_name column for auto-generated reviews (since they don't have real user_id)
    - Modify constraints to allow null user_id for auto-generated reviews
    - Add indexes for better performance
    - Add RLS policies for auto-generated reviews
    
  2. Security
    - Update RLS policies to handle auto-generated reviews
    - Allow service role to insert auto-generated reviews
*/

-- Add new columns to reviews table
DO $$
BEGIN
  -- Add is_auto_generated column
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'reviews' AND column_name = 'is_auto_generated'
  ) THEN
    ALTER TABLE reviews ADD COLUMN is_auto_generated boolean DEFAULT false;
  END IF;

  -- Add user_name column for auto-generated reviews
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'reviews' AND column_name = 'user_name'
  ) THEN
    ALTER TABLE reviews ADD COLUMN user_name text;
  END IF;
END $$;

-- Modify the unique constraint to allow multiple auto-generated reviews per product
-- but still maintain uniqueness for real user reviews
DO $$
BEGIN
  -- Drop existing unique constraint
  IF EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'reviews_product_id_user_id_key' 
    AND table_name = 'reviews'
  ) THEN
    ALTER TABLE reviews DROP CONSTRAINT reviews_product_id_user_id_key;
  END IF;
  
  -- Add new unique constraint that only applies to non-auto-generated reviews
  -- This allows multiple auto-generated reviews per product but maintains uniqueness for real users
  CREATE UNIQUE INDEX IF NOT EXISTS reviews_product_user_unique 
    ON reviews (product_id, user_id) 
    WHERE is_auto_generated = false AND user_id IS NOT NULL;
END $$;

-- Add check constraint to ensure auto-generated reviews have user_name
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'reviews_auto_generated_check' 
    AND table_name = 'reviews'
  ) THEN
    ALTER TABLE reviews ADD CONSTRAINT reviews_auto_generated_check 
      CHECK (
        (is_auto_generated = false AND user_id IS NOT NULL) OR 
        (is_auto_generated = true AND user_name IS NOT NULL)
      );
  END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_reviews_auto_generated ON reviews(is_auto_generated);
CREATE INDEX IF NOT EXISTS idx_reviews_product_auto ON reviews(product_id, is_auto_generated);
CREATE INDEX IF NOT EXISTS idx_reviews_user_name ON reviews(user_name) WHERE is_auto_generated = true;

-- Update RLS policies to handle auto-generated reviews

-- Allow service role to insert auto-generated reviews
CREATE POLICY "Service role can create auto-generated reviews"
  ON reviews
  FOR INSERT
  TO service_role
  WITH CHECK (is_auto_generated = true);

-- Allow service role to view all reviews (needed for management)
CREATE POLICY "Service role can view all reviews"
  ON reviews
  FOR SELECT
  TO service_role
  USING (true);

-- Allow service role to update auto-generated reviews if needed
CREATE POLICY "Service role can update auto-generated reviews"
  ON reviews
  FOR UPDATE
  TO service_role
  USING (is_auto_generated = true)
  WITH CHECK (is_auto_generated = true);

-- Allow service role to delete auto-generated reviews if needed
CREATE POLICY "Service role can delete auto-generated reviews"
  ON reviews
  FOR DELETE
  TO service_role
  USING (is_auto_generated = true);

-- Update the existing user policies to work with the new structure
-- Users can still only create reviews for themselves (non-auto-generated)
DROP POLICY IF EXISTS "Users can create own reviews" ON reviews;
CREATE POLICY "Users can create own reviews"
  ON reviews
  FOR INSERT
  TO public
  WITH CHECK (auth.uid() = user_id AND is_auto_generated = false);

-- Users can still only update their own reviews (non-auto-generated)
DROP POLICY IF EXISTS "Users can update own reviews" ON reviews;
CREATE POLICY "Users can update own reviews"
  ON reviews
  FOR UPDATE
  TO public
  USING (auth.uid() = user_id AND is_auto_generated = false)
  WITH CHECK (auth.uid() = user_id AND is_auto_generated = false);

-- Users can still only delete their own reviews (non-auto-generated)
DROP POLICY IF EXISTS "Users can delete own reviews" ON reviews;
CREATE POLICY "Users can delete own reviews"
  ON reviews
  FOR DELETE
  TO public
  USING (auth.uid() = user_id AND is_auto_generated = false);

-- Create a function to clean up old auto-generated reviews if needed
CREATE OR REPLACE FUNCTION cleanup_auto_generated_reviews(product_uuid UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- This function can be used to remove auto-generated reviews for a specific product
  -- if needed for maintenance or if real reviews start coming in
  DELETE FROM reviews 
  WHERE product_id = product_uuid AND is_auto_generated = true;
  
  -- Recalculate product rating after cleanup
  PERFORM update_product_rating(product_uuid);
END;
$$;

-- Grant execute permission to service role
GRANT EXECUTE ON FUNCTION cleanup_auto_generated_reviews(UUID) TO service_role;
