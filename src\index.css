/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom utilities for mobile optimization */
@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    /* Prevent automatic scrolling issues */
    scroll-behavior: auto;
    /* Ensure consistent scroll position */
    scroll-padding-top: 0;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    /* Prevent layout shifts */
    overflow-x: hidden;
    /* Ensure consistent positioning */
    position: relative;
  }

  /* Prevent layout shifts from images */
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }

  .font-playfair {
    font-family: 'Playfair Display', serif;
  }

  /* Improved text rendering */
  h1, h2, h3, h4, h5, h6 {
    text-rendering: optimizeLegibility;
    font-feature-settings: "kern" 1;
  }

  /* Line clamping utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Image optimization */
  img {
    /* Improve image loading performance */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    /* Prevent layout shift during loading */
    width: 100%;
    height: auto;
  }

  /* Preload critical images */
  img[loading="eager"] {
    /* High priority for above-the-fold images */
    content-visibility: auto;
  }

  img[loading="lazy"] {
    /* Optimize lazy-loaded images */
    content-visibility: auto;
    contain-intrinsic-size: 300px 200px;
  }
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #FFD700 0%, #FF8C00 50%, #DC143C 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #FFC700 0%, #FF7C00 50%, #CC133C 100%);
}

/* Remove smooth scrolling that can cause issues */
html {
  scroll-behavior: auto !important;
}

/* Focus styles for accessibility */
.focus-visible {
  @apply outline-none ring-2 ring-festive-gold ring-offset-2;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
  50% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.6); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Mobile-first responsive utilities */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  /* Improved touch targets for mobile */
  button, a {
    min-height: 44px;
    min-width: 44px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-festive-gradient {
    background: #000;
    color: #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  html {
    scroll-behavior: auto !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Enhanced button styles */
.btn-festive {
  @apply bg-festive-gradient text-white font-semibold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
}

.btn-festive:hover {
  box-shadow: 0 10px 25px rgba(220, 20, 60, 0.3);
}

/* Card hover effects */
.card-hover {
  @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
}

/* Gradient text */
.text-gradient {
  @apply bg-festive-gradient bg-clip-text text-transparent;
}

/* Mobile navigation improvements */
@media (max-width: 1024px) {
  .mobile-nav {
    @apply fixed inset-0 z-50 bg-white transform transition-transform duration-300;
  }
  
  .mobile-nav.closed {
    @apply translate-x-full;
  }
}

/* Improved form styles */
input:focus, textarea:focus, select:focus {
  @apply ring-2 ring-festive-gold ring-offset-2 border-festive-gold;
}

/* Better image loading */
img {
  @apply transition-opacity duration-300;
}

img[loading="lazy"] {
  @apply opacity-0;
}

img[loading="lazy"].loaded {
  @apply opacity-100;
}

/* Enhanced accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip link for keyboard navigation */
.skip-link {
  @apply absolute -top-10 left-4 bg-festive-gradient text-white px-4 py-2 rounded-md z-50 transition-all duration-300;
}

.skip-link:focus {
  @apply top-4;
}

/* Fix for layout shifts and scroll issues */
#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Ensure consistent header positioning */
header {
  position: sticky;
  top: 0;
  z-index: 50;
}

/* Prevent content jumping */
main {
  flex: 1;
  position: relative;
}

/* Image optimization for better loading */
img {
  /* Improve loading performance */
  will-change: auto;
  /* Prevent layout shift */
  aspect-ratio: attr(width) / attr(height);
}

/* Critical image preloading */
.hero-image {
  /* High priority loading for hero images */
  content-visibility: auto;
  contain-intrinsic-size: 800px 600px;
}

/* Lazy loading optimization */
.lazy-image {
  /* Optimize lazy-loaded images */
  content-visibility: auto;
  contain-intrinsic-size: 400px 300px;
}