import { supabase } from '@/integrations/supabase/client';
import { generateProductReviews, calculateAverageRating } from '@/utils/reviewGenerator';

export interface AutoGeneratedReview {
  product_id: string;
  user_name: string;
  rating: number;
  comment: string;
  created_at: string;
  is_auto_generated: boolean;
}

/**
 * Generates and inserts automatic reviews for a new product
 * @param productId - The ID of the product to generate reviews for
 * @param reviewCount - Number of reviews to generate (default: random between 5-10)
 * @returns Promise with the generated reviews and updated product rating
 */
export async function generateAutoReviewsForProduct(
  productId: string, 
  reviewCount?: number
): Promise<{
  success: boolean;
  reviews?: AutoGeneratedReview[];
  averageRating?: number;
  error?: string;
}> {
  try {
    // Generate random number of reviews between 5-10 if not specified
    const numReviews = reviewCount || Math.floor(Math.random() * 6) + 5; // 5-10 reviews
    
    // Generate the reviews
    const generatedReviews = generateProductReviews(numReviews);
    
    // Prepare reviews for database insertion
    const reviewsToInsert: AutoGeneratedReview[] = generatedReviews.map(review => ({
      product_id: productId,
      user_name: review.name,
      rating: review.rating,
      comment: review.review,
      created_at: review.createdAt.toISOString(),
      is_auto_generated: true
    }));

    // Insert reviews into database
    const { data: insertedReviews, error: insertError } = await supabase
      .from('reviews')
      .insert(reviewsToInsert)
      .select();
    
    if (insertError) {
      console.error('Error inserting auto-generated reviews:', insertError);
      return { success: false, error: insertError.message };
    }
    
    // Calculate average rating
    const averageRating = calculateAverageRating(generatedReviews);
    const reviewCount = generatedReviews.length;
    
    // Update product with new rating and review count
    const { error: updateError } = await supabase
      .from('products')
      .update({
        rating: averageRating,
        review_count: reviewCount,
        updated_at: new Date().toISOString()
      })
      .eq('id', productId);
    
    if (updateError) {
      console.error('Error updating product rating:', updateError);
      return { success: false, error: updateError.message };
    }
    
    console.log(`Successfully generated ${numReviews} reviews for product ${productId} with average rating ${averageRating}`);
    
    return {
      success: true,
      reviews: insertedReviews || reviewsToInsert,
      averageRating
    };
    
  } catch (error) {
    console.error('Error in generateAutoReviewsForProduct:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

/**
 * Checks if a product already has auto-generated reviews
 * @param productId - The ID of the product to check
 * @returns Promise<boolean> - true if product already has auto-generated reviews
 */
export async function hasAutoGeneratedReviews(productId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('reviews')
      .select('id')
      .eq('product_id', productId)
      .eq('is_auto_generated', true)
      .limit(1);
    
    if (error) {
      console.error('Error checking for auto-generated reviews:', error);
      return false;
    }
    
    return (data && data.length > 0);
  } catch (error) {
    console.error('Error in hasAutoGeneratedReviews:', error);
    return false;
  }
}

/**
 * Generates reviews for all products that don't have any reviews yet
 * This can be used as a one-time migration or periodic task
 */
export async function generateReviewsForProductsWithoutReviews(): Promise<{
  success: boolean;
  processedProducts: number;
  errors: string[];
}> {
  try {
    // Get all products that have no reviews or very low review count
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('id, name, review_count')
      .or('review_count.is.null,review_count.lt.3')
      .eq('is_active', true);
    
    if (fetchError) {
      console.error('Error fetching products:', fetchError);
      return { success: false, processedProducts: 0, errors: [fetchError.message] };
    }
    
    if (!products || products.length === 0) {
      console.log('No products found that need auto-generated reviews');
      return { success: true, processedProducts: 0, errors: [] };
    }
    
    const errors: string[] = [];
    let processedCount = 0;
    
    // Process each product
    for (const product of products) {
      // Check if product already has auto-generated reviews
      const hasAutoReviews = await hasAutoGeneratedReviews(product.id);
      
      if (!hasAutoReviews) {
        console.log(`Generating reviews for product: ${product.name} (ID: ${product.id})`);
        
        const result = await generateAutoReviewsForProduct(product.id);
        
        if (result.success) {
          processedCount++;
          console.log(`✓ Generated reviews for ${product.name}`);
        } else {
          errors.push(`Failed to generate reviews for ${product.name}: ${result.error}`);
          console.error(`✗ Failed to generate reviews for ${product.name}:`, result.error);
        }
        
        // Add small delay to avoid overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100));
      } else {
        console.log(`Skipping ${product.name} - already has auto-generated reviews`);
      }
    }
    
    console.log(`Completed processing. Successfully processed: ${processedCount}, Errors: ${errors.length}`);
    
    return {
      success: errors.length === 0,
      processedProducts: processedCount,
      errors
    };
    
  } catch (error) {
    console.error('Error in generateReviewsForProductsWithoutReviews:', error);
    return {
      success: false,
      processedProducts: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error occurred']
    };
  }
}

/**
 * Hook to automatically generate reviews when a new product is created
 * This should be called after a product is successfully created
 */
export async function onProductCreated(productId: string, productName: string): Promise<void> {
  try {
    console.log(`New product created: ${productName} (ID: ${productId}). Generating auto reviews...`);
    
    // Small delay to ensure product is fully created
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const result = await generateAutoReviewsForProduct(productId);
    
    if (result.success) {
      console.log(`✓ Auto-generated ${result.reviews?.length} reviews for new product: ${productName}`);
    } else {
      console.error(`✗ Failed to auto-generate reviews for new product ${productName}:`, result.error);
    }
  } catch (error) {
    console.error('Error in onProductCreated hook:', error);
  }
}
